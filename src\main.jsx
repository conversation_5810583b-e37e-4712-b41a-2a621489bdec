import React, { Suspense, useState } from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import { AuthContext } from "./authContext";

import SnackBar from "Components/SnackBar";
import PublicHeader from "Components/PublicHeader";
import TopHeader from "Components/TopHeader";
import SessionExpiredModal from "Components/SessionExpiredModal";

import AdminHeader from "Components/AdminHeader";
import MemberHeader from "Components/MemberHeader";

import NotFoundPage from "Pages/NotFoundPage";

import AdminLoginPage from "Pages/AdminLoginPage";
import AdminForgotPage from "Pages/AdminForgotPage";
import AdminResetPage from "Pages/AdminResetPage";

import MemberLoginPage from "Pages/MemberLoginPage";
import MemberForgotPage from "Pages/MemberForgotPage";
import MemberResetPage from "Pages/MemberResetPage";

import DashboardPage from "Pages/DashboardPage";
import ProfilePage from "Pages/ProfilePage";

import AdminEmailListPage from "Pages/AdminEmailListPage";
import AddAdminEmailPage from "Pages/AddAdminEmailPage";
import EditAdminEmailPage from "Pages/EditAdminEmailPage";

import UserMagicLoginPage from "Pages/MagicLogin/UserMagicLoginPage";
import MagicLoginVerifyPage from "Pages/MagicLogin/MagicLoginVerifyPage";
import ListMemberSettingPage from "Pages/ListMemberSettingPage";
import AdminUserListPage from "Pages/AdminUserListPage";
import AddAdminUserPage from "Pages/AddAdminUserPage";
import EditAdminUserPage from "Pages/EditAdminUserPage";

import AddClientPage from "Pages/AddClientPage";
import EditClientPage from "Pages/EditClientPage";
import ViewClientPage from "Pages/ViewClientPage";
import ListClientPage from "Pages/ListClientPage";

import AddProjectPage from "Pages/AddProjectPage";
import EditProjectPage from "Pages/EditProjectPage";
import ViewProjectPage from "Pages/ViewProjectPage";
import ListProjectPage from "Pages/ListProjectPage";

import AddMixTypePage from "Pages/AddMixTypePage";
import EditMixTypePage from "Pages/EditMixTypePage";
import ViewMixTypePage from "Pages/ViewMixTypePage";
import ListMixTypePage from "Pages/ListMixTypePage";

import AddWorkOrderPage from "Pages/AddWorkOrderPage";
import EditWorkOrderPage from "Pages/EditWorkOrderPage";
import ViewWorkOrderPage from "Pages/ViewWorkOrderPage";
import ListWorkOrderPage from "Pages/ListWorkOrderPage";

import AddEmployeePage from "Pages/AddEmployeePage";
import EditEmployeePage from "Pages/EditEmployeePage";
import ViewEmployeePage from "Pages/ViewEmployeePage";
import ListEmployeePage from "Pages/ListEmployeePage";

import ProjectCalendar from "Pages/ProjectCalendar";

// public pages
import SurveyPage from "Pages/SurveyPage";
import WriterWorkOrderPage from "Pages/PublicWorkOrder/WriterWorkOrderPage";
import ArtistWorkOrderPage from "Pages/PublicWorkOrder/ArtistWorkOrderPage";
import EngineerWorkOrderPage from "Pages/PublicWorkOrder/EngineerWorkOrderPage";
import ArtistEngineerWorkOrderPage from "Pages/PublicWorkOrder/ArtistEngineerWorkOrderPage";
import ProducerWorkOrderPage from "Pages/PublicWorkOrder/ProducerWorkOrderPage";
import WriterArtistWorkOrderPage from "Pages/PublicWorkOrder/WriterArtistWorkOrderPage";
import WriterArtistEngineerWorkOrderPage from "Pages/PublicWorkOrder/WriterArtistEngineerWorkOrderPage";

import AddMixSeasonPage from "Pages/AddMixSeasonPage";
import EditMixSeasonPage from "Pages/EditMixSeasonPage";
import ListMixSeasonPage from "Pages/ListMixSeasonPage";
import ViewMixSeasonPage from "Pages/ViewMixSeasonPage";
import MasterProjectPage from "Pages/MasterProjectPage";

import CountTracksMember from "Pages/CountTracksMember";
import PlansPage from "Pages/PlansPage";

import HomePage from "Pages/HomePage";

import ListUnAssignedSongsPage from "Pages/ListUnAssignedSongsPage";
import ListAdminSettingPage from "Pages/ListAdminSettingPage";

import ClientForgotPage from "Pages/Client/ClientForgotPage";
import ClientLoginPage from "Pages/Client/ClientLoginPage";
import ClientResetPage from "Pages/Client/ClientResetPage";
import AdminListProjectPage from "Pages/AdminListProjectPage";
import AdminListMixSeasonPage from "Pages/AdminListMixSeasonPage";
import AdminListWorkOrderPage from "Pages/AdminListWorkOrderPage";
import AdminListClientPage from "Pages/AdminListClientPage";
import AdminListUnAssignedSongsPage from "Pages/AdminListUnAssignedSongsPage";
import AdminMasterProjectPage from "Pages/AdminMasterProjectPage";
import AdminListEmployeePage from "Pages/AdminListEmployeePage";
import AdminListMixTypePage from "Pages/AdminListMixTypePage";
import AdminSubscriptionListPage from "Pages/AdminSubscriptionListPage";
import ClientHeader from "Components/Client/ClientHeader";
import ClientListProjectPage from "Pages/Client/ClientListProjectPage";
import ClientViewProjectPage from "Pages/Client/ClientViewProjectPage";
import ClientInvoicePage from "Pages/Client/ClientInvoicePage";
import ClientViewInvoicePage from "Pages/Client/ClientViewInvoicePage";
import AdminProjectCalendar from "Pages/AdminProjectCalendar";
import ManagerHeader from "Components/managerHeader";
import ManagerListMixSeasonPage from "Pages/manager/ManagerListMixSeasonPage";
import ManagerProjectCalendar from "Pages/manager/ManagerProjectCalendar";
import ManagerListMixTypePage from "Pages/manager/ManagerListMixTypePage";
import ManagerListProjectPage from "Pages/manager/ManagerListProjectPage";
import ManagerListClientPage from "Pages/manager/ManagerListClientPage";
import ManagerEmailListPage from "Pages/manager/ManagerEmailListPage";
import ManagerLoginPage from "Pages/manager/ManagerLoginPage";
import ManagerForgotPage from "Pages/manager/ManagerForgotPage";
import ManagerResetPage from "Pages/manager/ManagerResetPage";
import AddProjectPageManager from "Pages/manager/AddProjectPageManager";
import AddClientPageManager from "Pages/manager/AddClientManager";
import ViewClientPageManager from "Pages/manager/viewClientManager";
import EditClientPageManager from "Pages/manager/editClientManager";
import AddClientPageAdmin from "Pages/AdminAddClient";
import AddProjectPageAdmin from "Pages/AddProjectAdmin";
import ManagerViewProjectPage from "Pages/manager/ManagerViewProject";
import EditProjectPageManager from "Pages/manager/editProjectManager";
import AdminViewProjectPage from "Pages/ViewProjectAdmin";
import AdminEditProjectPage from "Pages/AdminEditProject";
import AddMixSeasonPageAdmin from "Pages/AddMixSeasonAdmin";
import AddMixSeasonPageManager from "Pages/manager/AddMixSeasonManager";

import ViewClientPageAdmin from "Pages/viewClientAdmin";
import CountTracksClient from "Pages/Client/CountTracksClient";
import CountTracksManager from "Pages/manager/CountTracksManager";
import CountTracksAdmin from "Pages/CountTracksAdmin";
import ClientEditsPage from "Pages/Client/ClientEditsPage";
import MemberEditsPage from "Pages/MemberEditsPage";
import ClientEditViewPage from "Pages/Client/ClientEditViewPage";
import MemberEditViewPage from "Pages/MemberEditViewPage";
import ViewMemberEditTypePage from "Pages/ViewMemberEditsType";
import AdminEditViewPage from "Pages/AdminEditViewPage";
import AdminEditsPage from "Pages/AdminEditPage";
import ManagerEditsPage from "Pages/manager/ManagerEditPage";
import ManagerEditViewPage from "Pages/manager/ManagerEditViewPage";
import ViewManagerEditTypePage from "Pages/ViewManagerEditsType";
import ViewAdminEditTypePage from "Pages/ViewAdminEditsType";
import { GlobalContext } from "./globalContext";
import InvoicePage from "Pages/InvoicePage";
import AdminStripePlansListPage from "Pages/Admin/List/AdminStripePlansListPage";
import AdminStripePricesListPage from "Pages/Admin/List/AdminStripePricesListPage";
import SetupSubscriptionPlansPage from "Pages/Admin/SetupSubscriptionPlansPage";
import MemberSubscriptionPage from "Pages/Member/MemberSubscriptionPage";
import Spinner from "Components/Spinner";
import MemberOnboardingPage from "Pages/Member/MemberOnboardingPage";
import InvoiceLinkPage from "Pages/InvoiceLinkPage";
import InvoiceSuccessPage from "Pages/InvoiceSuccessPage";
import InvoicePublicSuccessPage from "Pages/InvoicePublicSuccessPage";
import InvoiceClientSuccessPage from "Pages/InvoiceClientSuccessPage";
import ViewInvoicePage from "Pages/ViewInvoicePage";
import AdminSubscriptionManagementPage from "Pages/Admin/List/AdminSubscriptionManagementPage";
import ManagerSettingsPage from "Pages/manager/ManagerSettingsPage";
import ManagerInvoicePage from "Pages/manager/ManagerInvoicePage";
import ManagerViewInvoicePage from "Pages/manager/ManagerViewInvoicePage";
import AddMixTypePageManager from "Pages/manager/AddMixTypePageManager";
import EditMixTypePageManager from "Pages/manager/EditMixTypePageManager";
import StripeRefreshOnboardingPage from "Pages/Member/StripeRefreshOnboardingPage";
import StripeOnboardingCompletePage from "Pages/Member/StripeOnboardingCompletePage";

// import ClientHeader from 'Components/Client/ClientHeader';
// import ClientListProjectPage from "Pages/Client/ClientListProjectPage";
// import ClientViewProjectPage from "Pages/Client/ClientViewProjectPage";

function renderHeader(role) {
  switch (role) {
    case "admin":
      return <AdminHeader />;
    case "member":
      return <MemberHeader />;
    case "client":
      return <ClientHeader />;
    case "manager":
      return <ManagerHeader />;
    default:
      return <PublicHeader />;
  }
}

function renderRoutes(role) {
  switch (role) {
    case "none":
      return (
        <Routes>
          <Route
            exact
            path="/survey/:project_id"
            element={<SurveyPage />}
          ></Route>

          <Route
            exact
            path="/work-order/writer/:id"
            element={<WriterWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/artist/:id"
            element={<ArtistWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/engineer/:id"
            element={<EngineerWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/engineer-artist/:id"
            element={<ArtistEngineerWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/producer/:id"
            element={<ProducerWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/writer-artist/:id"
            element={<WriterArtistWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/writer-artist-engineer/:id"
            element={<WriterArtistEngineerWorkOrderPage />}
          ></Route>
          <Route exact path="/admin/login" element={<AdminLoginPage />}></Route>
          <Route
            exact
            path="/admin/forgot"
            element={<AdminForgotPage />}
          ></Route>
          <Route exact path="/admin/reset" element={<AdminResetPage />}></Route>
          <Route
            exact
            path="/member/login"
            element={<MemberLoginPage />}
          ></Route>
          <Route
            exact
            path="/member/forgot"
            element={<MemberForgotPage />}
          ></Route>
          <Route
            exact
            path="/member/reset"
            element={<MemberResetPage />}
          ></Route>
          <Route
            exact
            path="/manager/login"
            element={<ManagerLoginPage />}
          ></Route>
          <Route
            exact
            path="/manager/forgot"
            element={<ManagerForgotPage />}
          ></Route>
          <Route
            exact
            path="/manager/count-tracks"
            element={<CountTracksManager />}
          ></Route>
          <Route
            exact
            path="/manager/reset"
            element={<ManagerResetPage />}
          ></Route>
          <Route path="*" exact element={<NotFoundPage />}></Route>
          <Route path="/magic-login" element={<UserMagicLoginPage />}></Route>
          <Route
            path="/magic-login/verify"
            element={<MagicLoginVerifyPage />}
          ></Route>
          <Route exact path="/" element={<HomePage />}></Route>
          <Route exact path="/admin/login" element={<AdminLoginPage />}></Route>
          <Route
            exact
            path="/admin/forgot"
            element={<AdminForgotPage />}
          ></Route>
          <Route exact path="/admin/reset" element={<AdminResetPage />}></Route>
          <Route
            exact
            path="/member/login"
            element={<MemberLoginPage />}
          ></Route>
          <Route
            exact
            path="/member/forgot"
            element={<MemberForgotPage />}
          ></Route>
          <Route
            exact
            path="/member/reset"
            element={<MemberResetPage />}
          ></Route>
          <Route
            exact
            path="/manager/login"
            element={<ManagerLoginPage />}
          ></Route>
          <Route
            exact
            path="/manager/forgot"
            element={<ManagerForgotPage />}
          ></Route>
          <Route
            exact
            path="/manager/reset"
            element={<ManagerResetPage />}
          ></Route>
          <Route
            exact
            path="/client/login"
            element={<ClientLoginPage />}
          ></Route>
          <Route
            exact
            path="/client/forgot"
            element={<ClientForgotPage />}
          ></Route>
          <Route
            exact
            path="/client/reset"
            element={<ClientResetPage />}
          ></Route>
          <Route path="/magic-login" element={<UserMagicLoginPage />}></Route>
          <Route
            path="/magic-login/verify"
            element={<MagicLoginVerifyPage />}
          ></Route>

          {/* Public Invoice Routes */}
          <Route
            path="/invoice/:invoiceId/:token"
            element={<InvoiceLinkPage />}
          ></Route>
          <Route
            path="/invoice/success/:invoiceId"
            element={<InvoiceSuccessPage />}
          ></Route>
          <Route
            path="/invoice/public-success"
            element={<InvoicePublicSuccessPage />}
          ></Route>

          <Route path="*" exact element={<NotFoundPage />}></Route>
        </Routes>
      );
    case "client":
      return (
        <Routes>
          <Route exact path="/" element={<HomePage />}></Route>
          <Route
            path="/client/login"
            element={<Navigate to="/client/projects" replace />}
          ></Route>
          <Route
            path="/client/forgot"
            element={<Navigate to="/client/projects" replace />}
          ></Route>
          <Route
            path="/client/reset"
            element={<Navigate to="/client/projects" replace />}
          ></Route>
          <Route
            path="/client/projects"
            element={<ClientListProjectPage />}
          ></Route>
          <Route exact path="/client/profile" element={<ProfilePage />}></Route>
          <Route
            path="/client/view-project/:id"
            element={<ClientViewProjectPage />}
          ></Route>
          <Route
            path="/client/count-tracks"
            element={<CountTracksClient />}
          ></Route>
          <Route path="/client/edits" element={<ClientEditsPage />}></Route>
          <Route
            path="/client/invoices"
            element={<ClientInvoicePage />}
          ></Route>
          <Route
            path="/client/invoice/:id"
            element={<ClientViewInvoicePage />}
          ></Route>
          <Route
            path="/client/view-edit/:id/:project_id"
            element={<ClientEditViewPage />}
          ></Route>
          <Route
            path="/invoice/client-success"
            element={<InvoiceClientSuccessPage />}
          ></Route>
        </Routes>
      );
    case "manager":
      return (
        <Routes>
          <Route exact path="/" element={<HomePage />}></Route>
          <Route
            path="/manager/login"
            element={<Navigate to="/manager/projects" replace />}
          ></Route>
          <Route
            path="/stripe/refresh-onboarding"
            element={<StripeRefreshOnboardingPage />}
          ></Route>
          <Route
            path="/stripe/onboarding-complete"
            element={<StripeOnboardingCompletePage />}
          ></Route>
          <Route path="/manager/settings" element={<ManagerSettingsPage />} />
          <Route path="/manager/invoices" element={<ManagerInvoicePage />} />
          <Route
            path="/manager/invoice/:id"
            element={<ManagerViewInvoicePage />}
          />
          <Route
            path="/manager/forgot"
            element={<Navigate to="/manager/projects" replace />}
          ></Route>
          <Route
            path="/manager/reset"
            element={<Navigate to="/manager/projects" replace />}
          ></Route>
          <Route
            exact
            path="/manager/profile"
            element={<ProfilePage />}
          ></Route>
          <Route
            path="/manager/emails"
            element={<ManagerEmailListPage />}
          ></Route>
          <Route
            path="/manager/add-client"
            element={<AddClientPageManager />}
          ></Route>
          <Route
            path="/manager/edit-client/:id"
            element={<EditClientPageManager />}
          ></Route>
          <Route
            path="/manager/view-client/:id"
            element={<ViewClientPageManager />}
          ></Route>
          <Route
            path="/manager/clients"
            element={<ManagerListClientPage />}
          ></Route>
          <Route
            path="/manager/add-project"
            element={<AddProjectPageManager />}
          ></Route>
          <Route
            path="/manager/edit-project/:id/:user_id"
            element={<EditProjectPageManager />}
          ></Route>
          <Route
            path="/manager/view-project/:id"
            element={<ManagerViewProjectPage />}
          ></Route>
          <Route
            path="/"
            element={<Navigate to="/manager/projects" replace />}
          />

          <Route
            path="/manager/projects"
            element={<ManagerListProjectPage />}
          ></Route>
          <Route
            path="/manager/add-mix-type"
            element={<AddMixTypePageManager />}
          ></Route>
          <Route
            path="/manager/edit-mix-type/:id"
            element={<EditMixTypePageManager />}
          ></Route>
          <Route
            path="/manager/view-mix-type/:id"
            element={<ViewMixTypePage />}
          ></Route>
          <Route
            path="/manager/mix-types"
            element={<ManagerListMixTypePage />}
          ></Route>
          <Route
            path="/manager/project-calendar"
            element={<ManagerProjectCalendar />}
          ></Route>
          <Route
            path="/manager/add-mix-season"
            element={<AddMixSeasonPageManager />}
          ></Route>
          <Route
            path="/manager/edit-mix-season/:id"
            element={<EditMixSeasonPage />}
          ></Route>
          <Route
            path="/manager/view-mix-season/:id"
            element={<ViewMixSeasonPage />}
          ></Route>
          <Route
            path="/manager/count-tracks"
            element={<CountTracksManager />}
          ></Route>
          <Route
            path="/manager/mix-seasons"
            element={<ManagerListMixSeasonPage />}
          ></Route>
          <Route
            path="/manager/view-edit/:id/:project_id"
            element={<ManagerEditViewPage />}
          ></Route>
          <Route path="/manager/edits/" element={<ManagerEditsPage />}></Route>
          <Route
            path="/manager/edits-type-view/:id"
            element={<ViewManagerEditTypePage />}
          ></Route>
        </Routes>
      );
    case "admin":
      return (
        <Routes>
          <Route exact path="/" element={<HomePage />}></Route>
          <Route path="/admin/dashboard" element={<DashboardPage />}></Route>
          <Route path="/admin/emails" element={<AdminEmailListPage />}></Route>
          <Route
            path="/admin/plans"
            element={
              <Suspense fallback={<Spinner />}>
                <AdminStripePlansListPage />
              </Suspense>
            }
          ></Route>
          <Route
            path="/admin/pricing"
            element={
              <Suspense fallback={<Spinner />}>
                <AdminStripePricesListPage />
              </Suspense>
            }
          ></Route>
          <Route
            path="/admin/add-email"
            element={<AddAdminEmailPage />}
          ></Route>
          <Route
            path="/admin/edit-email/:id"
            element={<EditAdminEmailPage />}
          ></Route>
          <Route
            path="/admin/add-client"
            element={<AddClientPageAdmin />}
          ></Route>
          <Route
            path="/admin/edit-client/:id"
            element={<EditClientPage />}
          ></Route>
          <Route
            path="/admin/view-client/:id"
            element={<ViewClientPageAdmin />}
          ></Route>
          <Route
            path="/admin/clients"
            element={<AdminListClientPage />}
          ></Route>
          <Route
            path="/admin/add-project"
            element={<AddProjectPageAdmin />}
          ></Route>
          <Route
            path="/admin/edit-project/:id/:user_id"
            element={<AdminEditProjectPage />}
          ></Route>
          <Route
            path="/admin/view-project/:id"
            element={<AdminViewProjectPage />}
          ></Route>
          <Route
            path="/admin/master-project-view"
            element={<AdminMasterProjectPage />}
          ></Route>
          <Route
            path="/admin/projects"
            element={<AdminListProjectPage />}
          ></Route>
          <Route
            path="/admin/add-mix-type"
            element={<AddMixTypePage />}
          ></Route>
          <Route
            path="/admin/edit-mix-type/:id"
            element={<EditMixTypePage />}
          ></Route>
          <Route
            path="/admin/view-mix-type/:id"
            element={<ViewMixTypePage />}
          ></Route>
          <Route
            path="/admin/mix-types"
            element={<AdminListMixTypePage />}
          ></Route>
          <Route
            path="/admin/add-work-order"
            element={<AddWorkOrderPage />}
          ></Route>
          <Route
            path="/admin/edit-work-order/:id"
            element={<EditWorkOrderPage />}
          ></Route>
          <Route
            path="/admin/view-work-order/:id"
            element={<ViewWorkOrderPage />}
          ></Route>
          <Route
            path="/admin/work-orders"
            element={<AdminListWorkOrderPage />}
          ></Route>
          <Route
            path="/admin/add-employee"
            element={<AddEmployeePage />}
          ></Route>
          <Route
            path="/admin/edit-employee/:id"
            element={<EditEmployeePage />}
          ></Route>
          <Route
            path="/admin/view-employee/:id"
            element={<ViewEmployeePage />}
          ></Route>
          <Route
            path="/admin/employees"
            element={<AdminListEmployeePage />}
          ></Route>
          <Route
            path="/admin/count-tracks"
            element={<CountTracksAdmin />}
          ></Route>
          <Route
            path="/admin/setting"
            element={<ListAdminSettingPage />}
          ></Route>
          <Route path="/admin/users" element={<AdminUserListPage />}></Route>
          <Route path="/admin/add-user" element={<AddAdminUserPage />}></Route>
          <Route
            path="/admin/edit-user/:id"
            element={<EditAdminUserPage />}
          ></Route>
          <Route
            path="/admin/project-calendar"
            element={<AdminProjectCalendar />}
          ></Route>
          <Route
            path="/admin/add-mix-season"
            element={<AddMixSeasonPageAdmin />}
          ></Route>
          <Route
            path="/admin/subscription"
            element={<AdminSubscriptionListPage />}
          ></Route>
          <Route
            path="/admin/edit-mix-season/:id"
            element={<EditMixSeasonPage />}
          ></Route>
          <Route
            path="/admin/view-mix-season/:id"
            element={<ViewMixSeasonPage />}
          ></Route>
          <Route
            path="/admin/mix-seasons"
            element={<AdminListMixSeasonPage />}
          ></Route>
          <Route
            path="/admin/unassigned-songs"
            element={<AdminListUnAssignedSongsPage />}
          ></Route>
          <Route
            path="/admin/view-edit/:id/:project_id"
            element={<AdminEditViewPage />}
          ></Route>
          <Route path="/admin/edits/" element={<AdminEditsPage />}></Route>
          <Route
            path="/admin/edits-type-view/:id"
            element={<ViewAdminEditTypePage />}
          ></Route>
          <Route
            path="/admin/subscriptions"
            element={<AdminSubscriptionListPage />}
          ></Route>
          <Route
            path="/admin/stripe-plans"
            element={<AdminStripePlansListPage />}
          ></Route>
          <Route
            path="/admin/stripe-prices"
            element={<AdminStripePricesListPage />}
          ></Route>
          <Route
            path="/admin/setup-subscription-plans"
            element={<SetupSubscriptionPlansPage />}
          ></Route>
          <Route
            path="/admin/subscription-management"
            element={
              <Suspense fallback={<Spinner />}>
                <AdminSubscriptionManagementPage />
              </Suspense>
            }
          ></Route>
        </Routes>
      );

    case "member":
      return (
        <Routes>
          <Route exact path="/" element={<HomePage />}></Route>
          <Route
            path="/member/login"
            element={<Navigate to="/member/dashboard" replace />}
          ></Route>
          <Route path="/member/forgot" element={<MemberForgotPage />}></Route>
          <Route path="/member/reset" element={<MemberResetPage />}></Route>
          <Route
            path="/member/onboarding"
            element={<MemberOnboardingPage />}
          ></Route>
          <Route
            path="/stripe/refresh-onboarding"
            element={<StripeRefreshOnboardingPage />}
          ></Route>
          <Route
            path="/stripe/onboarding-complete"
            element={<StripeOnboardingCompletePage />}
          ></Route>
          <Route path="/member/dashboard" element={<DashboardPage />}></Route>
          <Route path="/member/profile" element={<ProfilePage />}></Route>
          <Route
            path="/member/subscription"
            element={<MemberSubscriptionPage />}
          ></Route>
          <Route path="/member/plans" element={<PlansPage />}></Route>
          <Route path="/member/calendar" element={<ProjectCalendar />}></Route>
          <Route
            path="/member/count-tracks"
            element={<CountTracksMember />}
          ></Route>
          <Route path="/member/edits" element={<MemberEditsPage />}></Route>
          <Route
            path="/member/view-edit/:id/:project_id"
            element={<MemberEditViewPage />}
          ></Route>
          <Route
            path="/member/view-edit-type/:id/:project_id"
            element={<ViewMemberEditTypePage />}
          ></Route>
          <Route
            path="/member/setting"
            element={<ListMemberSettingPage />}
          ></Route>
          <Route path="/member/add-client" element={<AddClientPage />}></Route>
          <Route
            path="/member/edit-client/:id"
            element={<EditClientPage />}
          ></Route>
          <Route
            path="/member/view-client/:id"
            element={<ViewClientPage />}
          ></Route>
          <Route path="/member/clients" element={<ListClientPage />}></Route>
          <Route
            path="/member/add-project"
            element={<AddProjectPage />}
          ></Route>
          <Route
            path="/member/edit-project/:id"
            element={<EditProjectPage />}
          ></Route>
          <Route
            path="/member/view-project/:id"
            element={<ViewProjectPage />}
          ></Route>
          <Route path="/member/projects" element={<ListProjectPage />}></Route>
          <Route
            path="/member/project-calendar"
            element={<ProjectCalendar />}
          ></Route>
          <Route
            path="/member/add-mix-type"
            element={<AddMixTypePage />}
          ></Route>
          <Route
            path="/member/edit-mix-type/:id"
            element={<EditMixTypePage />}
          ></Route>
          <Route
            path="/member/view-mix-type/:id"
            element={<ViewMixTypePage />}
          ></Route>
          <Route path="/member/mix-types" element={<ListMixTypePage />}></Route>
          <Route
            path="/member/add-work-order"
            element={<AddWorkOrderPage />}
          ></Route>
          <Route
            path="/member/edit-work-order/:id"
            element={<EditWorkOrderPage />}
          ></Route>
          <Route
            path="/member/view-work-order/:id"
            element={<ViewWorkOrderPage />}
          ></Route>
          <Route
            path="/member/work-orders"
            element={<ListWorkOrderPage />}
          ></Route>
          <Route
            path="/member/add-employee"
            element={<AddEmployeePage />}
          ></Route>
          <Route
            path="/member/edit-employee/:id"
            element={<EditEmployeePage />}
          ></Route>
          <Route
            path="/member/view-employee/:id"
            element={<ViewEmployeePage />}
          ></Route>
          <Route
            path="/member/employees"
            element={<ListEmployeePage />}
          ></Route>
          <Route
            path="/member/add-mix-season"
            element={<AddMixSeasonPage />}
          ></Route>
          <Route
            path="/member/edit-mix-season/:id"
            element={<EditMixSeasonPage />}
          ></Route>
          <Route
            path="/member/view-mix-season/:id"
            element={<ViewMixSeasonPage />}
          ></Route>
          <Route
            path="/member/mix-seasons"
            element={<ListMixSeasonPage />}
          ></Route>
          <Route
            path="/member/master-project-view"
            element={<MasterProjectPage />}
          ></Route>
          <Route
            path="/member/unassigned-songs"
            element={<ListUnAssignedSongsPage />}
          ></Route>
          <Route path="/member/invoices" element={<InvoicePage />}></Route>
          <Route
            path="/member/invoice/:id"
            element={<ViewInvoicePage />}
          ></Route>
        </Routes>
      );

    default:
      return (
        <Routes>
          <Route
            exact
            path="/survey/:project_id"
            element={<SurveyPage />}
          ></Route>

          <Route
            exact
            path="/work-order/writer/:id"
            element={<WriterWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/artist/:id"
            element={<ArtistWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/engineer/:id"
            element={<EngineerWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/engineer-artist/:id"
            element={<ArtistEngineerWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/producer/:id"
            element={<ProducerWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/writer-artist/:id"
            element={<WriterArtistWorkOrderPage />}
          ></Route>
          <Route
            exact
            path="/work-order/writer-artist-engineer/:id"
            element={<WriterArtistEngineerWorkOrderPage />}
          ></Route>
          <Route exact path="/admin/login" element={<AdminLoginPage />}></Route>
          <Route
            exact
            path="/admin/forgot"
            element={<AdminForgotPage />}
          ></Route>
          <Route exact path="/admin/reset" element={<AdminResetPage />}></Route>
          <Route
            exact
            path="/member/login"
            element={<MemberLoginPage />}
          ></Route>
          <Route
            exact
            path="/member/forgot"
            element={<MemberForgotPage />}
          ></Route>
          <Route
            exact
            path="/member/reset"
            element={<MemberResetPage />}
          ></Route>
          <Route
            exact
            path="/manager/login"
            element={<ManagerLoginPage />}
          ></Route>
          <Route
            exact
            path="/manager/forgot"
            element={<ManagerForgotPage />}
          ></Route>
          <Route
            exact
            path="/manager/count-tracks"
            element={<CountTracksManager />}
          ></Route>
          <Route
            exact
            path="/manager/reset"
            element={<ManagerResetPage />}
          ></Route>
          <Route path="*" exact element={<NotFoundPage />}></Route>
          <Route path="/magic-login" element={<UserMagicLoginPage />}></Route>
          <Route
            path="/magic-login/verify"
            element={<MagicLoginVerifyPage />}
          ></Route>
          <Route
            exact
            path="/client/login"
            element={<ClientLoginPage />}
          ></Route>
          <Route
            exact
            path="/client/forgot"
            element={<ClientForgotPage />}
          ></Route>
          <Route
            exact
            path="/client/reset"
            element={<ClientResetPage />}
          ></Route>
          <Route
            exact
            path="/invoice/:invoiceId/:token"
            element={<InvoiceLinkPage />}
          ></Route>
          <Route
            exact
            path="/invoice/success/:invoiceId"
            element={<InvoiceSuccessPage />}
          ></Route>
          <Route
            exact
            path="/invoice/public-success"
            element={<InvoicePublicSuccessPage />}
          ></Route>
        </Routes>
      );
  }
}

function Main() {
  console.log(React.useContext(AuthContext));
  const { state } = React.useContext(AuthContext);
  const { state: GlobalState } = React.useContext(GlobalContext);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  if (location.pathname === "/") {
    return (
      <div className="h-full bg-boxdark-2 text-bodydark dark:bg-boxdark-2 dark:text-bodydark">
        <PublicHeader />
        <HomePage />
        <SessionExpiredModal />
        <SnackBar />
      </div>
    );
  } else if (location.pathname === "/member/onboarding") {
    return (
      <div className="h-full bg-boxdark-2 text-bodydark dark:bg-boxdark-2 dark:text-bodydark">
        <PublicHeader />
        <MemberOnboardingPage />
      </div>
    );
  }

  return (
    <div className="h-full bg-boxdark-2 text-bodydark dark:bg-boxdark-2 dark:text-bodydark">
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        {state.isAuthenticated && (
          <div
            className={` left-0 top-0 z-[9] h-screen ${
              state.role === "client" ? "w-[180px]" : "w-[220px]"
            } flex-shrink-0 bg-boxdark duration-300 ease-in-out ${
              GlobalState.isOpen
                ? "left-[0px] translate-x-0 md:left-0 lg:static "
                : "fixed -translate-x-full lg:-translate-x-full"
            }`}
          >
            {renderHeader(state.role)}
          </div>
        )}

        {/* Main Content */}
        <div
          id="mainContainer"
          className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden"
        >
          {/* Top Header */}
          {state.isAuthenticated && (
            <header className="sticky top-0 z-[9] flex w-full bg-boxdark drop-shadow-none">
              <TopHeader />
            </header>
          )}

          {/* Main Content Area */}
          <main>
            <div className="h-full w-full">
              {!state.isAuthenticated ? (
                <>
                  <PublicHeader />
                  {renderRoutes("none")}
                  <SessionExpiredModal />
                  <SnackBar />
                </>
              ) : (
                <>
                  {renderRoutes(state.role)}
                  <SessionExpiredModal />
                  <SnackBar />
                </>
              )}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}

export default Main;
